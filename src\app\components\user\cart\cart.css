.cart-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
}

.header h2 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.subtitle {
  font-size: 1.1rem;
  color: #7f8c8d;
  margin: 0;
}

/* Loading & Error States */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container {
  display: flex;
  justify-content: center;
  padding: 4rem 2rem;
}

.error-message {
  text-align: center;
  padding: 2rem;
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 12px;
  max-width: 400px;
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.error-message p {
  color: #e53e3e;
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
}

.retry-btn {
  background: #e53e3e;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s ease;
}

.retry-btn:hover {
  background: #c53030;
}

/* Empty Cart */
.empty-cart {
  text-align: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  opacity: 0.5;
}

.empty-cart h3 {
  font-size: 1.8rem;
  color: #2c3e50;
  margin-bottom: 1rem;
}

.empty-cart p {
  color: #7f8c8d;
  font-size: 1.1rem;
  margin-bottom: 2rem;
}

.shop-btn {
  display: inline-block;
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  text-decoration: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.shop-btn:hover {
  background: linear-gradient(135deg, #2980b9, #1f5f8b);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
}

/* Cart Content */
.cart-content {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 3rem;
  align-items: start;
}

/* Cart Items */
.cart-items {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.items-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.items-header h3 {
  font-size: 1.3rem;
  color: #2c3e50;
  margin: 0;
}

.clear-cart-btn {
  background: none;
  border: 1px solid #e74c3c;
  color: #e74c3c;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.clear-cart-btn:hover {
  background: #e74c3c;
  color: white;
}

/* Cart Item */
.cart-item {
  display: flex;
  padding: 2rem;
  border-bottom: 1px solid #e9ecef;
  transition: background-color 0.3s ease;
}

.cart-item:hover {
  background: #f8f9fa;
}

.cart-item:last-child {
  border-bottom: none;
}

.item-image {
  width: 120px;
  height: 120px;
  border-radius: 12px;
  overflow: hidden;
  margin-right: 1.5rem;
  flex-shrink: 0;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-details {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.item-info {
  flex: 1;
  margin-right: 2rem;
}

.item-brand {
  color: #3498db;
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.5rem;
}

.item-name {
  font-size: 1.2rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  line-height: 1.3;
}

.item-description {
  color: #7f8c8d;
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 1rem;
}

.item-options {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.option {
  background: #f8f9fa;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  color: #6c757d;
  border: 1px solid #e9ecef;
}

.item-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 1rem;
  min-width: 200px;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 0.25rem;
}

.qty-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1.1rem;
  font-weight: 600;
  color: #3498db;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qty-btn:hover:not(:disabled) {
  background: #3498db;
  color: white;
}

.qty-btn:disabled {
  color: #bdc3c7;
  cursor: not-allowed;
}

.quantity {
  min-width: 40px;
  text-align: center;
  font-weight: 600;
  color: #2c3e50;
}

.item-pricing {
  text-align: right;
}

.unit-price {
  font-size: 0.85rem;
  color: #7f8c8d;
  margin-bottom: 0.25rem;
}

.total-price {
  font-size: 1.2rem;
  font-weight: 700;
  color: #27ae60;
}

.remove-btn {
  background: none;
  border: 1px solid #e74c3c;
  color: #e74c3c;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.remove-btn:hover {
  background: #e74c3c;
  color: white;
}

.remove-icon {
  font-size: 0.9rem;
}

/* Cart Summary */
.cart-summary {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 2rem;
  position: sticky;
  top: 2rem;
}

.cart-summary h3 {
  font-size: 1.3rem;
  color: #2c3e50;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e9ecef;
}

.summary-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  font-size: 1rem;
}

.summary-line.discount {
  color: #27ae60;
}

.summary-line.total {
  font-size: 1.2rem;
  font-weight: 700;
  color: #2c3e50;
  border-top: 2px solid #e9ecef;
  margin-top: 1rem;
  padding-top: 1rem;
}

.free-shipping {
  color: #27ae60;
  font-weight: 600;
}

.checkout-actions {
  margin-top: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.checkout-btn {
  background: linear-gradient(135deg, #27ae60, #229954);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  text-align: center;
  display: block;
}

.checkout-btn:hover {
  background: linear-gradient(135deg, #229954, #1e7e34);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(39, 174, 96, 0.3);
}

.continue-shopping-btn {
  background: none;
  border: 2px solid #3498db;
  color: #3498db;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  text-align: center;
  display: block;
}

.continue-shopping-btn:hover {
  background: #3498db;
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .cart-container {
    padding: 1rem;
  }

  .header h2 {
    font-size: 2rem;
  }

  .cart-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .cart-summary {
    position: static;
  }

  .cart-item {
    flex-direction: column;
    gap: 1rem;
  }

  .item-image {
    width: 100%;
    height: 200px;
    margin-right: 0;
  }

  .item-details {
    flex-direction: column;
    gap: 1rem;
  }

  .item-info {
    margin-right: 0;
  }

  .item-actions {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    min-width: auto;
  }

  .item-pricing {
    text-align: left;
  }

  .items-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .cart-container {
    padding: 0.5rem;
  }

  .header h2 {
    font-size: 1.8rem;
  }

  .cart-item {
    padding: 1.5rem;
  }

  .item-image {
    height: 150px;
  }

  .item-actions {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .quantity-controls {
    justify-content: center;
  }

  .checkout-actions {
    gap: 0.75rem;
  }

  .checkout-btn,
  .continue-shopping-btn {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }
}
<div class="products-container">
  <div class="header">
    <h2>Nos Produits Moto 🏍️</h2>
    <p class="subtitle">Découvrez notre collection d'équipements moto de qualité</p>
  </div>

  <!-- Loading State -->
  @if (loadingState.isLoading) {
    <div class="loading-container">
      <div class="spinner"></div>
      <p>Chargement des produits...</p>
    </div>
  }

  <!-- Error State -->
  @if (loadingState.error && !loadingState.isLoading) {
    <div class="error-container">
      <div class="error-message">
        <i class="error-icon">⚠️</i>
        <p>{{ loadingState.error }}</p>
        <button (click)="loadProducts()" class="retry-btn">Réessayer</button>
      </div>
    </div>
  }

  <!-- Products Grid -->
  @if (!loadingState.isLoading && !loadingState.error) {
    <div class="products-grid">
      @for (product of products; track product.id) {
        <div class="product-card" [routerLink]="['/product', product.id]">

          <div class="product-image">
            <img [src]="product.image" [alt]="product.name" />
            @if (!product.inStock) {
              <div class="product-badge">Rupture de stock</div>
            }
            @if (product.rating && product.rating >= 4.5) {
              <div class="product-badge sale">Top vente</div>
            }
          </div>

          <div class="product-info">
            @if (product.brand) {
              <div class="product-brand">{{ product.brand }}</div>
            }
            <h3 class="product-name">{{ product.name }}</h3>
            @if (product.description) {
              <p class="product-description">{{ product.description }}</p>
            }

            @if (product.rating) {
              <div class="product-rating">
                <div class="stars">
                  @for (star of getStars(product.rating); track star) {
                    <span class="star" [class.filled]="star <= product.rating">★</span>
                  }
                </div>
                <span class="rating-value">({{ product.rating }})</span>
              </div>
            }

            <div class="product-footer">
              <div class="price-container">
                <span class="price">{{ product.price }} TND</span>
              </div>

              <div class="stock-info">
                @if (product.inStock && product.quantity) {
                  <span class="in-stock">{{ product.quantity }} en stock</span>
                }
                @if (!product.inStock) {
                  <span class="out-of-stock">Rupture de stock</span>
                }
              </div>
            </div>

            <button class="add-to-cart-btn"
                    [disabled]="!product.inStock"
                    (click)="onAddToCart($event, product)">
              <i class="cart-icon">🛒</i>
              {{ product.inStock ? 'Ajouter au panier' : 'Indisponible' }}
            </button>
          </div>
        </div>
      }
    </div>
  }

  <!-- Empty State -->
  @if (!loadingState.isLoading && !loadingState.error && products.length === 0) {
    <div class="empty-state">
      <div class="empty-icon">📦</div>
      <h3>Aucun produit disponible</h3>
      <p>Revenez plus tard pour découvrir nos nouveaux produits</p>
    </div>
  }
</div>

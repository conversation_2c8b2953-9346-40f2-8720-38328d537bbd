import { Component, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { AuthService } from '../../../services/auth';
import {
  RegisterRequest,
  LoadingState,
  ValidationError
} from '../../../interfaces';

@Component({
  selector: 'app-register',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './register.html',
  styleUrls: ['./register.css']
})
export class RegisterComponent implements OnDestroy {
  registerData: RegisterRequest = {
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    phone: '',
    acceptTerms: false
  };

  loadingState: LoadingState = {
    isLoading: false,
    error: undefined
  };

  validationErrors: ValidationError[] = [];
  showPassword = false;
  showConfirmPassword = false;

  private subscriptions: Subscription[] = [];

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  onRegister(): void {
    // Reset errors
    this.validationErrors = [];
    this.loadingState.error = undefined;

    // Validate form
    if (!this.validateForm()) {
      return;
    }

    this.loadingState.isLoading = true;

    const registerSub = this.authService.register(this.registerData).subscribe({
      next: (response) => {
        this.loadingState.isLoading = false;

        if (response.success) {
          // Redirect to products page
          this.router.navigate(['/products']);
        } else {
          this.loadingState.error = response.message || 'Erreur lors de l\'inscription';
        }
      },
      error: (error) => {
        console.error('Register error:', error);
        this.loadingState.isLoading = false;
        this.loadingState.error = 'Erreur de connexion. Veuillez réessayer.';
      }
    });

    this.subscriptions.push(registerSub);
  }

  private validateForm(): boolean {
    this.validationErrors = [];

    // First name validation
    if (!this.registerData.firstName.trim()) {
      this.validationErrors.push({
        field: 'firstName',
        message: 'Le prénom est requis',
        code: 'required'
      });
    }

    // Last name validation
    if (!this.registerData.lastName.trim()) {
      this.validationErrors.push({
        field: 'lastName',
        message: 'Le nom est requis',
        code: 'required'
      });
    }

    // Email validation
    if (!this.registerData.email) {
      this.validationErrors.push({
        field: 'email',
        message: 'L\'email est requis',
        code: 'required'
      });
    } else if (!this.isValidEmail(this.registerData.email)) {
      this.validationErrors.push({
        field: 'email',
        message: 'Format d\'email invalide',
        code: 'invalid_format'
      });
    }

    // Phone validation (optional but if provided, should be valid)
    if (this.registerData.phone && !this.isValidPhone(this.registerData.phone)) {
      this.validationErrors.push({
        field: 'phone',
        message: 'Format de téléphone invalide',
        code: 'invalid_format'
      });
    }

    // Password validation
    if (!this.registerData.password) {
      this.validationErrors.push({
        field: 'password',
        message: 'Le mot de passe est requis',
        code: 'required'
      });
    } else if (this.registerData.password.length < 8) {
      this.validationErrors.push({
        field: 'password',
        message: 'Le mot de passe doit contenir au moins 8 caractères',
        code: 'min_length'
      });
    } else if (!this.isStrongPassword(this.registerData.password)) {
      this.validationErrors.push({
        field: 'password',
        message: 'Le mot de passe doit contenir au moins une majuscule, une minuscule et un chiffre',
        code: 'weak_password'
      });
    }

    // Confirm password validation
    if (!this.registerData.confirmPassword) {
      this.validationErrors.push({
        field: 'confirmPassword',
        message: 'La confirmation du mot de passe est requise',
        code: 'required'
      });
    } else if (this.registerData.password !== this.registerData.confirmPassword) {
      this.validationErrors.push({
        field: 'confirmPassword',
        message: 'Les mots de passe ne correspondent pas',
        code: 'password_mismatch'
      });
    }

    // Terms acceptance validation
    if (!this.registerData.acceptTerms) {
      this.validationErrors.push({
        field: 'acceptTerms',
        message: 'Vous devez accepter les conditions d\'utilisation',
        code: 'required'
      });
    }

    return this.validationErrors.length === 0;
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private isValidPhone(phone: string): boolean {
    const phoneRegex = /^(\+216|216)?[0-9]{8}$/;
    return phoneRegex.test(phone.replace(/\s/g, ''));
  }

  private isStrongPassword(password: string): boolean {
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    return hasUpperCase && hasLowerCase && hasNumbers;
  }

  getFieldError(fieldName: string): string | null {
    const error = this.validationErrors.find(err => err.field === fieldName);
    return error ? error.message : null;
  }

  hasFieldError(fieldName: string): boolean {
    return this.validationErrors.some(err => err.field === fieldName);
  }

  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }

  toggleConfirmPasswordVisibility(): void {
    this.showConfirmPassword = !this.showConfirmPassword;
  }

  onLoginRedirect(): void {
    this.router.navigate(['/login']);
  }

  getPasswordStrength(): { strength: string; color: string; width: string } {
    const password = this.registerData.password;

    if (!password) {
      return { strength: '', color: '#e9ecef', width: '0%' };
    }

    let score = 0;

    // Length check
    if (password.length >= 8) score++;
    if (password.length >= 12) score++;

    // Character variety checks
    if (/[a-z]/.test(password)) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/[0-9]/.test(password)) score++;
    if (/[^A-Za-z0-9]/.test(password)) score++;

    if (score <= 2) {
      return { strength: 'Faible', color: '#e74c3c', width: '33%' };
    } else if (score <= 4) {
      return { strength: 'Moyen', color: '#f39c12', width: '66%' };
    } else {
      return { strength: 'Fort', color: '#27ae60', width: '100%' };
    }
  }
}

<div class="register-container">
  <div class="register-card">
    <div class="register-header">
      <h2><PERSON><PERSON>er un compte 🚀</h2>
      <p class="subtitle">Rejoignez MotoShop et découvrez nos produits exclusifs</p>
    </div>

    <!-- Loading State -->
    @if (loadingState.isLoading) {
      <div class="loading-overlay">
        <div class="spinner"></div>
        <p>Création du compte...</p>
      </div>
    }

    <!-- Error Message -->
    @if (loadingState.error) {
      <div class="error-banner">
        <i class="error-icon">⚠️</i>
        <span>{{ loadingState.error }}</span>
      </div>
    }

    <form (ngSubmit)="onRegister()" class="register-form" [class.loading]="loadingState.isLoading">
      <!-- Name Fields -->
      <div class="name-row">
        <div class="form-group">
          <label for="firstName">Prénom *</label>
          <div class="input-wrapper">
            <input
              type="text"
              id="firstName"
              name="firstName"
              [(ngModel)]="registerData.firstName"
              placeholder="Votre prénom"
              [class.error]="hasFieldError('firstName')"
              [disabled]="loadingState.isLoading"
              autocomplete="given-name"
              required />
            <i class="input-icon">👤</i>
          </div>
          @if (hasFieldError('firstName')) {
            <div class="field-error">
              {{ getFieldError('firstName') }}
            </div>
          }
        </div>

        <div class="form-group">
          <label for="lastName">Nom *</label>
          <div class="input-wrapper">
            <input
              type="text"
              id="lastName"
              name="lastName"
              [(ngModel)]="registerData.lastName"
              placeholder="Votre nom"
              [class.error]="hasFieldError('lastName')"
              [disabled]="loadingState.isLoading"
              autocomplete="family-name"
              required />
            <i class="input-icon">👤</i>
          </div>
          @if (hasFieldError('lastName')) {
            <div class="field-error">
              {{ getFieldError('lastName') }}
            </div>
          }
        </div>
      </div>

      <!-- Email Field -->
      <div class="form-group">
        <label for="email">Adresse email *</label>
        <div class="input-wrapper">
          <input
            type="email"
            id="email"
            name="email"
            [(ngModel)]="registerData.email"
            placeholder="<EMAIL>"
            [class.error]="hasFieldError('email')"
            [disabled]="loadingState.isLoading"
            autocomplete="email"
            required />
          <i class="input-icon">📧</i>
        </div>
        @if (hasFieldError('email')) {
          <div class="field-error">
            {{ getFieldError('email') }}
          </div>
        }
      </div>

      <!-- Phone Field -->
      <div class="form-group">
        <label for="phone">Téléphone (optionnel)</label>
        <div class="input-wrapper">
          <input
            type="tel"
            id="phone"
            name="phone"
            [(ngModel)]="registerData.phone"
            placeholder="+216 20 123 456"
            [class.error]="hasFieldError('phone')"
            [disabled]="loadingState.isLoading"
            autocomplete="tel" />
          <i class="input-icon">📱</i>
        </div>
        @if (hasFieldError('phone')) {
          <div class="field-error">
            {{ getFieldError('phone') }}
          </div>
        }
      </div>

      <!-- Password Field -->
      <div class="form-group">
        <label for="password">Mot de passe *</label>
        <div class="input-wrapper">
          <input
            [type]="showPassword ? 'text' : 'password'"
            id="password"
            name="password"
            [(ngModel)]="registerData.password"
            placeholder="Votre mot de passe"
            [class.error]="hasFieldError('password')"
            [disabled]="loadingState.isLoading"
            autocomplete="new-password"
            required />
          <button
            type="button"
            class="password-toggle"
            (click)="togglePasswordVisibility()"
            [disabled]="loadingState.isLoading">
            {{ showPassword ? '🙈' : '👁️' }}
          </button>
        </div>

        <!-- Password Strength Indicator -->
        @if (registerData.password) {
          <div class="password-strength">
            <div class="strength-bar">
              <div
                class="strength-fill"
                [style.width]="getPasswordStrength().width"
                [style.background-color]="getPasswordStrength().color">
              </div>
            </div>
            <span
              class="strength-text"
              [style.color]="getPasswordStrength().color">
              {{ getPasswordStrength().strength }}
            </span>
          </div>
        }

        @if (hasFieldError('password')) {
          <div class="field-error">
            {{ getFieldError('password') }}
          </div>
        }
      </div>

      <!-- Confirm Password Field -->
      <div class="form-group">
        <label for="confirmPassword">Confirmer le mot de passe *</label>
        <div class="input-wrapper">
          <input
            [type]="showConfirmPassword ? 'text' : 'password'"
            id="confirmPassword"
            name="confirmPassword"
            [(ngModel)]="registerData.confirmPassword"
            placeholder="Confirmez votre mot de passe"
            [class.error]="hasFieldError('confirmPassword')"
            [disabled]="loadingState.isLoading"
            autocomplete="new-password"
            required />
          <button
            type="button"
            class="password-toggle"
            (click)="toggleConfirmPasswordVisibility()"
            [disabled]="loadingState.isLoading">
            {{ showConfirmPassword ? '🙈' : '👁️' }}
          </button>
        </div>
        @if (hasFieldError('confirmPassword')) {
          <div class="field-error">
            {{ getFieldError('confirmPassword') }}
          </div>
        }
      </div>

      <!-- Terms Acceptance -->
      <div class="form-group checkbox-group">
        <label class="checkbox-label">
          <input
            type="checkbox"
            [(ngModel)]="registerData.acceptTerms"
            name="acceptTerms"
            [disabled]="loadingState.isLoading"
            required>
          <span class="checkmark"></span>
          J'accepte les <a href="/terms" target="_blank">conditions d'utilisation</a> et la <a href="/privacy" target="_blank">politique de confidentialité</a>
        </label>
        @if (hasFieldError('acceptTerms')) {
          <div class="field-error">
            {{ getFieldError('acceptTerms') }}
          </div>
        }
      </div>

      <!-- Submit Button -->
      <button
        type="submit"
        class="register-btn"
        [disabled]="loadingState.isLoading">
        @if (loadingState.isLoading) {
          <span class="btn-spinner"></span>
          Création en cours...
        } @else {
          Créer mon compte
        }
      </button>
    </form>

    <!-- Login Link -->
    <div class="login-link">
      <p>Vous avez déjà un compte ?</p>
      <button
        class="login-redirect-btn"
        (click)="onLoginRedirect()"
        [disabled]="loadingState.isLoading">
        Se connecter
      </button>
    </div>
  </div>
</div>

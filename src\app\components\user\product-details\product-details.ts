import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

// Product interface for type safety
export interface Product {
  name: string;
  price: number;
  image: string;
  description?: string;
}

@Component({
  selector: 'app-product-details',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './product-details.html',
  styleUrls: ['./product-details.css']
})
export class ProductDetailsComponent {
  @Input() product: Product | null = null;

  onAddToCart(): void {
    if (this.product) {
      // TODO: Implement cart service integration
      console.log('Adding to cart:', this.product);
      // This would typically call a cart service
      // this.cartService.addItem(this.product);
    }
  }
}

import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { Subscription } from 'rxjs';
import { OrderService } from '../../../services/order';
import {
  OrderSummary,
  OrderStatus,
  OrderFilter,
  LoadingState
} from '../../../interfaces';

@Component({
  selector: 'app-orders',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './orders.html',
  styleUrls: ['./orders.css']
})
export class OrdersComponent implements OnInit, OnDestroy {
  orders: OrderSummary[] = [];
  loadingState: LoadingState = {
    isLoading: false,
    error: undefined
  };

  currentFilter: OrderFilter = {
    sortBy: 'date',
    sortOrder: 'desc'
  };

  selectedStatus: OrderStatus | 'all' = 'all';

  private subscriptions: Subscription[] = [];
  private currentUserId = 1; // Mock user ID

  constructor(private orderService: OrderService) {}

  ngOnInit(): void {
    this.loadOrders();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  loadOrders(): void {
    this.loadingState.isLoading = true;
    this.loadingState.error = undefined;

    const ordersSub = this.orderService.getUserOrders(this.currentUserId, this.currentFilter)
      .subscribe({
        next: (response) => {
          if (response.success && response.data) {
            this.orders = response.data.orders;
          } else {
            this.loadingState.error = response.message || 'Erreur lors du chargement des commandes';
          }
          this.loadingState.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading orders:', error);
          this.loadingState.error = 'Erreur de connexion';
          this.loadingState.isLoading = false;
        }
      });

    this.subscriptions.push(ordersSub);
  }

  onStatusFilterChange(status: OrderStatus | 'all'): void {
    this.selectedStatus = status;

    if (status === 'all') {
      this.currentFilter.status = undefined;
    } else {
      this.currentFilter.status = [status];
    }

    this.loadOrders();
  }

  onSortChange(sortBy: 'date' | 'amount' | 'status'): void {
    if (this.currentFilter.sortBy === sortBy) {
      // Toggle sort order
      this.currentFilter.sortOrder = this.currentFilter.sortOrder === 'asc' ? 'desc' : 'asc';
    } else {
      this.currentFilter.sortBy = sortBy;
      this.currentFilter.sortOrder = 'desc';
    }

    this.loadOrders();
  }

  getStatusText(status: OrderStatus): string {
    return this.orderService.getOrderStatusText(status);
  }

  getStatusColor(status: OrderStatus): string {
    return this.orderService.getOrderStatusColor(status);
  }

  trackByOrderId(index: number, order: OrderSummary): number {
    return order.id;
  }

  get filteredOrdersCount(): number {
    return this.orders.length;
  }

  get totalOrdersValue(): number {
    return this.orders.reduce((sum, order) => sum + order.total, 0);
  }

  formatDate(date: Date): string {
    return new Intl.DateTimeFormat('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date));
  }

  getOrderStatusIcon(status: OrderStatus): string {
    const iconMap: Record<OrderStatus, string> = {
      'pending': '⏳',
      'confirmed': '✅',
      'processing': '📦',
      'shipped': '🚚',
      'delivered': '✅',
      'cancelled': '❌',
      'refunded': '💰',
      'returned': '↩️'
    };

    return iconMap[status] || '📋';
  }

  canCancelOrder(order: OrderSummary): boolean {
    return ['pending', 'confirmed'].includes(order.status);
  }

  canTrackOrder(order: OrderSummary): boolean {
    return ['shipped'].includes(order.status);
  }

  onCancelOrder(orderId: number): void {
    if (confirm('Êtes-vous sûr de vouloir annuler cette commande ?')) {
      // TODO: Implement order cancellation
      console.log('Cancelling order:', orderId);
    }
  }

  onTrackOrder(orderId: number): void {
    // TODO: Implement order tracking
    console.log('Tracking order:', orderId);
  }

  onReorderItems(orderId: number): void {
    // TODO: Implement reorder functionality
    console.log('Reordering items from order:', orderId);
  }
}

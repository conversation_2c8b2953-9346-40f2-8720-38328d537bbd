import { Routes } from '@angular/router';
import { HomeComponent } from './components/home/<USER>';
import { ProductsComponent } from './components/user/products/products';
import { ProductDetailsComponent } from './components/user/product-details/product-details';
import { LoginComponent } from './components/user/login/login';
import { RegisterComponent } from './components/user/register/register';
import { CartComponent } from './components/user/cart/cart';
import { CheckoutComponent } from './components/user/checkout/checkout';
import { OrdersComponent } from './components/user/orders/orders';
import { ProfileComponent } from './components/user/profile/profile';

export const routes: Routes = [
  { path: '', component: HomeComponent },
  { path: 'products', component: ProductsComponent },
  { path: 'product/:id', component: ProductDetailsComponent },
  { path: 'login', component: LoginComponent },
  { path: 'register', component: RegisterComponent },
  { path: 'cart', component: CartComponent },
  { path: 'checkout', component: CheckoutComponent },
  { path: 'orders', component: OrdersComponent },
  { path: 'profile', component: ProfileComponent },
];

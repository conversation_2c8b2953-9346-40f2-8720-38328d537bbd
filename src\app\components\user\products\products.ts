import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ProductService } from '../../../services/product';
import { Product, LoadingState } from '../../../interfaces';

@Component({
  selector: 'app-products',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './products.html',
  styleUrls: ['./products.css']
})
export class ProductsComponent implements OnInit {
  products: Product[] = [];
  loadingState: LoadingState = {
    isLoading: false,
    error: undefined
  };

  constructor(private productService: ProductService) {}

  ngOnInit(): void {
    this.loadProducts();
  }

  loadProducts(): void {
    this.loadingState.isLoading = true;
    this.loadingState.error = undefined;

    this.productService.getAllProducts().subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.products = response.data;
        } else {
          this.loadingState.error = response.message || 'Erreur lors du chargement des produits';
        }
        this.loadingState.isLoading = false;
      },
      error: (error) => {
        this.loadingState.error = 'Erreur de connexion';
        this.loadingState.isLoading = false;
        console.error('Error loading products:', error);
      }
    });
  }

  onProductClick(productId: number): void {
    // Navigation will be handled by routerLink in template
    console.log('Product clicked:', productId);
  }

  trackByProductId(index: number, product: Product): number {
    return product.id;
  }

  getStars(rating: number): number[] {
    return Array(5).fill(0).map((_, i) => i + 1);
  }

  onAddToCart(event: Event, product: Product): void {
    event.preventDefault();
    event.stopPropagation();

    if (!product.inStock) {
      return;
    }

    // TODO: Implement cart service integration
    console.log('Adding to cart:', product);
    // This would typically call a cart service
    // this.cartService.addItem({ productId: product.id, quantity: 1 });
  }
}

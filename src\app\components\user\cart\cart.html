<div class="cart-container">
  <div class="header">
    <h2><PERSON> 🛒</h2>
    @if (itemCount > 0) {
      <p class="subtitle">{{ itemCount }} article{{ itemCount > 1 ? 's' : '' }} dans votre panier</p>
    }
  </div>

  <!-- Loading State -->
  @if (loadingState.isLoading) {
    <div class="loading-container">
      <div class="spinner"></div>
      <p>Chargement du panier...</p>
    </div>
  }

  <!-- Error State -->
  @if (loadingState.error && !loadingState.isLoading) {
    <div class="error-container">
      <div class="error-message">
        <i class="error-icon">⚠️</i>
        <p>{{ loadingState.error }}</p>
        <button (click)="loadCartSummary()" class="retry-btn">Réessayer</button>
      </div>
    </div>
  }

  <!-- Empty Cart -->
  @if (isEmpty && !loadingState.isLoading && !loadingState.error) {
    <div class="empty-cart">
      <div class="empty-icon">🛒</div>
      <h3>Votre panier est vide</h3>
      <p>Découvrez nos produits et ajoutez-les à votre panier</p>
      <a routerLink="/products" class="shop-btn">Voir les produits</a>
    </div>
  }

  <!-- Cart Content -->
  @if (!isEmpty && !loadingState.isLoading && !loadingState.error && cart) {
    <div class="cart-content">
      <!-- Cart Items -->
      <div class="cart-items">
        <div class="items-header">
          <h3>Articles ({{ itemCount }})</h3>
          <button (click)="clearCart()" class="clear-cart-btn">Vider le panier</button>
        </div>

        @for (item of cart.items; track item.id) {
          <div class="cart-item">
            <div class="item-image">
              <img [src]="item.product.image" [alt]="item.product.name" />
            </div>

            <div class="item-details">
              <div class="item-info">
                @if (item.product.brand) {
                  <div class="item-brand">{{ item.product.brand }}</div>
                }
                <h4 class="item-name">{{ item.product.name }}</h4>
                @if (item.product.description) {
                  <p class="item-description">{{ item.product.description }}</p>
                }

                @if (item.selectedOptions) {
                  <div class="item-options">
                    @if (item.selectedOptions.size) {
                      <span class="option">Taille: {{ item.selectedOptions.size }}</span>
                    }
                    @if (item.selectedOptions.color) {
                      <span class="option">Couleur: {{ item.selectedOptions.color }}</span>
                    }
                  </div>
                }
              </div>

              <div class="item-actions">
                <div class="quantity-controls">
                  <button (click)="updateQuantity(item.id, item.quantity - 1)"
                          class="qty-btn"
                          [disabled]="item.quantity <= 1">-</button>
                  <span class="quantity">{{ item.quantity }}</span>
                  <button (click)="updateQuantity(item.id, item.quantity + 1)"
                          class="qty-btn">+</button>
                </div>

                <div class="item-pricing">
                  <div class="unit-price">{{ item.unitPrice }} TND/unité</div>
                  <div class="total-price">{{ item.totalPrice }} TND</div>
                </div>

                <button (click)="removeItem(item.id)" class="remove-btn">
                  <i class="remove-icon">🗑️</i>
                  Supprimer
                </button>
              </div>
            </div>
          </div>
        }
      </div>

      <!-- Cart Summary -->
      @if (cartSummary) {
        <div class="cart-summary">
          <h3>Résumé de la commande</h3>

          <div class="summary-line">
            <span>Sous-total ({{ cartSummary.itemCount }} articles)</span>
            <span>{{ cartSummary.subtotal }} {{ cartSummary.currency }}</span>
          </div>

          @if (cartSummary.discount > 0) {
            <div class="summary-line discount">
              <span>Remise</span>
              <span>-{{ cartSummary.discount }} {{ cartSummary.currency }}</span>
            </div>
          }

          <div class="summary-line">
            <span>Livraison</span>
            <span>
              @if (cartSummary.shipping === 0) {
                <span class="free-shipping">Gratuite</span>
              } @else {
                {{ cartSummary.shipping }} {{ cartSummary.currency }}
              }
            </span>
          </div>

          <div class="summary-line">
            <span>TVA</span>
            <span>{{ cartSummary.tax }} {{ cartSummary.currency }}</span>
          </div>

          <div class="summary-line total">
            <span>Total</span>
            <span>{{ cartSummary.total }} {{ cartSummary.currency }}</span>
          </div>

          <div class="checkout-actions">
            <button routerLink="/checkout" class="checkout-btn">
              Procéder au paiement
            </button>
            <button routerLink="/products" class="continue-shopping-btn">
              Continuer mes achats
            </button>
          </div>
        </div>
      }
    </div>
  }
</div>

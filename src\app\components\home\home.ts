import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { ProductService } from '../../services/product';
import {
  Product,
  ProductCategory,
  LoadingState
} from '../../interfaces';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './home.html',
  styleUrls: ['./home.css']
})
export class HomeComponent implements OnInit {
  categories: ProductCategory[] = [];
  featuredProducts: Product[] = [];

  loadingState: LoadingState = {
    isLoading: true,
    error: undefined
  };

  stats = {
    totalProducts: 150,
    happyCustomers: 2500,
    yearsExperience: 15,
    deliveryTime: 24
  };

  constructor(
    private productService: ProductService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadData();
  }

  private loadData(): void {
    this.loadingState.isLoading = true;

    // Load categories
    this.productService.getCategories().subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.categories = response.data;
        }
      },
      error: (error) => {
        console.error('Error loading categories:', error);
      }
    });

    // Load featured products
    this.productService.getAllProducts().subscribe({
      next: (response) => {
        this.loadingState.isLoading = false;

        if (response.success && response.data) {
          // Take first 3 products as featured
          this.featuredProducts = response.data.slice(0, 3);
        }
      },
      error: (error) => {
        console.error('Error loading products:', error);
        this.loadingState.isLoading = false;
        this.loadingState.error = 'Erreur lors du chargement des produits';
      }
    });
  }

  onCategoryClick(category: ProductCategory): void {
    this.router.navigate(['/products'], {
      queryParams: { category: category.slug }
    });
  }

  onProductClick(product: Product): void {
    this.router.navigate(['/product', product.id]);
  }

  onShopNow(): void {
    this.router.navigate(['/products']);
  }

  onLoginClick(): void {
    this.router.navigate(['/login']);
  }

  onRegisterClick(): void {
    this.router.navigate(['/register']);
  }

  trackByCategory(index: number, category: ProductCategory): number {
    return category.id;
  }

  trackByProduct(index: number, product: Product): number {
    return product.id;
  }
}

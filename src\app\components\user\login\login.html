<div class="login-container">
  <div class="login-card">
    <div class="login-header">
      <h2>Connexion 🔐</h2>
      <p class="subtitle">Connectez-vous à votre compte MotoShop</p>
    </div>

    <!-- Loading State -->
    @if (loadingState.isLoading) {
      <div class="loading-overlay">
        <div class="spinner"></div>
        <p>Connexion en cours...</p>
      </div>
    }

    <!-- Error Message -->
    @if (loadingState.error) {
      <div class="error-banner">
        <i class="error-icon">⚠️</i>
        <span>{{ loadingState.error }}</span>
      </div>
    }

    <form (ngSubmit)="onLogin()" class="login-form" [class.loading]="loadingState.isLoading">
      <!-- Email Field -->
      <div class="form-group">
        <label for="email">Adresse email</label>
        <div class="input-wrapper">
          <input
            type="email"
            id="email"
            name="email"
            [(ngModel)]="loginData.email"
            placeholder="<EMAIL>"
            [class.error]="hasFieldError('email')"
            [disabled]="loadingState.isLoading"
            autocomplete="email"
            required />
          <i class="input-icon">📧</i>
        </div>
        @if (hasFieldError('email')) {
          <div class="field-error">
            {{ getFieldError('email') }}
          </div>
        }
      </div>

      <!-- Password Field -->
      <div class="form-group">
        <label for="password">Mot de passe</label>
        <div class="input-wrapper">
          <input
            [type]="showPassword ? 'text' : 'password'"
            id="password"
            name="password"
            [(ngModel)]="loginData.password"
            placeholder="Votre mot de passe"
            [class.error]="hasFieldError('password')"
            [disabled]="loadingState.isLoading"
            autocomplete="current-password"
            required />
          <button
            type="button"
            class="password-toggle"
            (click)="togglePasswordVisibility()"
            [disabled]="loadingState.isLoading">
            {{ showPassword ? '🙈' : '👁️' }}
          </button>
        </div>
        @if (hasFieldError('password')) {
          <div class="field-error">
            {{ getFieldError('password') }}
          </div>
        }
      </div>

      <!-- Remember Me -->
      <div class="form-group checkbox-group">
        <label class="checkbox-label">
          <input
            type="checkbox"
            [(ngModel)]="loginData.rememberMe"
            name="rememberMe"
            [disabled]="loadingState.isLoading">
          <span class="checkmark"></span>
          Se souvenir de moi
        </label>
      </div>

      <!-- Submit Button -->
      <button
        type="submit"
        class="login-btn"
        [disabled]="loadingState.isLoading">
        @if (loadingState.isLoading) {
          <span class="btn-spinner"></span>
          Connexion...
        } @else {
          Se connecter
        }
      </button>

      <!-- Forgot Password -->
      <div class="form-actions">
        <button
          type="button"
          class="forgot-password-btn"
          (click)="onForgotPassword()"
          [disabled]="loadingState.isLoading">
          Mot de passe oublié ?
        </button>
      </div>
    </form>

    <!-- Register Link -->
    <div class="register-link">
      <p>Pas encore de compte ?</p>
      <button
        class="register-btn"
        (click)="onRegisterRedirect()"
        [disabled]="loadingState.isLoading">
        Créer un compte
      </button>
    </div>
  </div>
</div>

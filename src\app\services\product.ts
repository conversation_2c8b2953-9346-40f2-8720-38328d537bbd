import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import {
  Product,
  ProductCategory,
  ProductFilter,
  ProductSearchResult,
  ApiResponse
} from '../interfaces';

@Injectable({
  providedIn: 'root'
})
export class ProductService {
  private mockProducts: Product[] = [
    {
      id: 1,
      name: 'Casque Shark Speed-R',
      price: 400,
      image: 'assets/images/casque.jpg',
      description: 'Casque intégral haute performance pour moto sportive',
      category: { id: 1, name: 'Casques', slug: 'casques' },
      brand: 'Shark',
      inStock: true,
      quantity: 15,
      rating: 4.5,
      specifications: {
        weight: '1.4kg',
        material: 'Fibre de carbone',
        color: 'Noir/Rouge',
        size: 'M'
      }
    },
    {
      id: 2,
      name: 'Gants Alpinestars GP Pro',
      price: 120,
      image: 'assets/images/gants.jpg',
      description: 'Gants de moto en cuir avec protection renforcée',
      category: { id: 2, name: 'Gants', slug: 'gants' },
      brand: 'Alpinestars',
      inStock: true,
      quantity: 25,
      rating: 4.2,
      specifications: {
        material: 'Cuir véritable',
        color: 'Noir',
        size: 'L'
      }
    },
    {
      id: 3,
      name: 'Blouson Dainese Racing',
      price: 350,
      image: 'assets/images/blouson.jpg',
      description: 'Blouson en cuir pour moto avec protections CE',
      category: { id: 3, name: 'Blousons', slug: 'blousons' },
      brand: 'Dainese',
      inStock: true,
      quantity: 8,
      rating: 4.7,
      specifications: {
        material: 'Cuir premium',
        color: 'Noir',
        size: 'L'
      }
    }
  ];

  private mockCategories: ProductCategory[] = [
    { id: 1, name: 'Casques', slug: 'casques', description: 'Casques de protection' },
    { id: 2, name: 'Gants', slug: 'gants', description: 'Gants de moto' },
    { id: 3, name: 'Blousons', slug: 'blousons', description: 'Vestes et blousons' },
    { id: 4, name: 'Bottes', slug: 'bottes', description: 'Chaussures de moto' }
  ];

  getAllProducts(): Observable<ApiResponse<Product[]>> {
    return of({
      success: true,
      data: this.mockProducts,
      message: 'Produits récupérés avec succès'
    });
  }

  getProductById(id: number): Observable<ApiResponse<Product>> {
    const product = this.mockProducts.find(p => p.id === id);
    return of({
      success: !!product,
      data: product,
      message: product ? 'Produit trouvé' : 'Produit non trouvé'
    });
  }

  searchProducts(filter: ProductFilter): Observable<ApiResponse<ProductSearchResult>> {
    let filteredProducts = [...this.mockProducts];

    // Apply filters
    if (filter.category) {
      filteredProducts = filteredProducts.filter(p =>
        p.category.slug === filter.category
      );
    }

    if (filter.minPrice !== undefined) {
      filteredProducts = filteredProducts.filter(p => p.price >= filter.minPrice!);
    }

    if (filter.maxPrice !== undefined) {
      filteredProducts = filteredProducts.filter(p => p.price <= filter.maxPrice!);
    }

    if (filter.brand) {
      filteredProducts = filteredProducts.filter(p =>
        p.brand?.toLowerCase().includes(filter.brand!.toLowerCase())
      );
    }

    if (filter.inStock !== undefined) {
      filteredProducts = filteredProducts.filter(p => p.inStock === filter.inStock);
    }

    // Apply sorting
    if (filter.sortBy) {
      filteredProducts.sort((a, b) => {
        let aValue: any, bValue: any;

        switch (filter.sortBy) {
          case 'price':
            aValue = a.price;
            bValue = b.price;
            break;
          case 'name':
            aValue = a.name;
            bValue = b.name;
            break;
          case 'rating':
            aValue = a.rating || 0;
            bValue = b.rating || 0;
            break;
          default:
            return 0;
        }

        if (filter.sortOrder === 'desc') {
          return bValue > aValue ? 1 : -1;
        }
        return aValue > bValue ? 1 : -1;
      });
    }

    const result: ProductSearchResult = {
      products: filteredProducts,
      totalCount: filteredProducts.length,
      currentPage: 1,
      totalPages: 1,
      filters: filter
    };

    return of({
      success: true,
      data: result,
      message: 'Recherche effectuée avec succès'
    });
  }

  getCategories(): Observable<ApiResponse<ProductCategory[]>> {
    return of({
      success: true,
      data: this.mockCategories,
      message: 'Catégories récupérées avec succès'
    });
  }
}

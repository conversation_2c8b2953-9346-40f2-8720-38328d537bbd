import { Component, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { AuthService } from '../../../services/auth';
import {
  LoginRequest,
  LoadingState,
  ValidationError
} from '../../../interfaces';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './login.html',
  styleUrls: ['./login.css']
})
export class LoginComponent implements OnDestroy {
  loginData: LoginRequest = {
    email: '',
    password: '',
    rememberMe: false
  };

  loadingState: LoadingState = {
    isLoading: false,
    error: undefined
  };

  validationErrors: ValidationError[] = [];
  showPassword = false;

  private subscriptions: Subscription[] = [];

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  onLogin(): void {
    // Reset errors
    this.validationErrors = [];
    this.loadingState.error = undefined;

    // Validate form
    if (!this.validateForm()) {
      return;
    }

    this.loadingState.isLoading = true;

    const loginSub = this.authService.login(this.loginData).subscribe({
      next: (response) => {
        this.loadingState.isLoading = false;

        if (response.success) {
          // Redirect to dashboard or previous page
          this.router.navigate(['/products']);
        } else {
          this.loadingState.error = response.message || 'Erreur de connexion';
        }
      },
      error: (error) => {
        console.error('Login error:', error);
        this.loadingState.isLoading = false;
        this.loadingState.error = 'Erreur de connexion. Veuillez réessayer.';
      }
    });

    this.subscriptions.push(loginSub);
  }

  private validateForm(): boolean {
    this.validationErrors = [];

    if (!this.loginData.email) {
      this.validationErrors.push({
        field: 'email',
        message: 'L\'email est requis',
        code: 'required'
      });
    } else if (!this.isValidEmail(this.loginData.email)) {
      this.validationErrors.push({
        field: 'email',
        message: 'Format d\'email invalide',
        code: 'invalid_format'
      });
    }

    if (!this.loginData.password) {
      this.validationErrors.push({
        field: 'password',
        message: 'Le mot de passe est requis',
        code: 'required'
      });
    } else if (this.loginData.password.length < 6) {
      this.validationErrors.push({
        field: 'password',
        message: 'Le mot de passe doit contenir au moins 6 caractères',
        code: 'min_length'
      });
    }

    return this.validationErrors.length === 0;
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  getFieldError(fieldName: string): string | null {
    const error = this.validationErrors.find(err => err.field === fieldName);
    return error ? error.message : null;
  }

  hasFieldError(fieldName: string): boolean {
    return this.validationErrors.some(err => err.field === fieldName);
  }

  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }

  onForgotPassword(): void {
    // TODO: Implement forgot password functionality
    console.log('Forgot password clicked');
  }

  onRegisterRedirect(): void {
    this.router.navigate(['/register']);
  }
}

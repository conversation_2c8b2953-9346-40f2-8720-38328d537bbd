<div class="orders-container">
  <div class="header">
    <h2>Mes Commandes 📦</h2>
    @if (filteredOrdersCount > 0) {
      <p class="subtitle">
        {{ filteredOrdersCount }} commande{{ filteredOrdersCount > 1 ? 's' : '' }}
        • Total: {{ totalOrdersValue }} TND
      </p>
    }
  </div>

  <!-- Filters -->
  <div class="filters-section">
    <div class="status-filters">
      <h3>Filtrer par statut</h3>
      <div class="filter-buttons">
        <button
          class="filter-btn"
          [class.active]="selectedStatus === 'all'"
          (click)="onStatusFilterChange('all')">
          Toutes
        </button>
        <button
          class="filter-btn"
          [class.active]="selectedStatus === 'pending'"
          (click)="onStatusFilterChange('pending')">
          En attente
        </button>
        <button
          class="filter-btn"
          [class.active]="selectedStatus === 'confirmed'"
          (click)="onStatusFilterChange('confirmed')">
          Confirmées
        </button>
        <button
          class="filter-btn"
          [class.active]="selectedStatus === 'shipped'"
          (click)="onStatusFilterChange('shipped')">
          Expédiées
        </button>
        <button
          class="filter-btn"
          [class.active]="selectedStatus === 'delivered'"
          (click)="onStatusFilterChange('delivered')">
          Livrées
        </button>
      </div>
    </div>

    <div class="sort-options">
      <h3>Trier par</h3>
      <div class="sort-buttons">
        <button
          class="sort-btn"
          [class.active]="currentFilter.sortBy === 'date'"
          (click)="onSortChange('date')">
          Date
          @if (currentFilter.sortBy === 'date') {
            <span class="sort-arrow">{{ currentFilter.sortOrder === 'asc' ? '↑' : '↓' }}</span>
          }
        </button>
        <button
          class="sort-btn"
          [class.active]="currentFilter.sortBy === 'amount'"
          (click)="onSortChange('amount')">
          Montant
          @if (currentFilter.sortBy === 'amount') {
            <span class="sort-arrow">{{ currentFilter.sortOrder === 'asc' ? '↑' : '↓' }}</span>
          }
        </button>
        <button
          class="sort-btn"
          [class.active]="currentFilter.sortBy === 'status'"
          (click)="onSortChange('status')">
          Statut
          @if (currentFilter.sortBy === 'status') {
            <span class="sort-arrow">{{ currentFilter.sortOrder === 'asc' ? '↑' : '↓' }}</span>
          }
        </button>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  @if (loadingState.isLoading) {
    <div class="loading-container">
      <div class="spinner"></div>
      <p>Chargement des commandes...</p>
    </div>
  }

  <!-- Error State -->
  @if (loadingState.error && !loadingState.isLoading) {
    <div class="error-container">
      <div class="error-message">
        <i class="error-icon">⚠️</i>
        <p>{{ loadingState.error }}</p>
        <button (click)="loadOrders()" class="retry-btn">Réessayer</button>
      </div>
    </div>
  }

  <!-- Orders List -->
  @if (!loadingState.isLoading && !loadingState.error) {
    @if (orders.length > 0) {
      <div class="orders-list">
        @for (order of orders; track order.id) {
          <div class="order-card">
            <div class="order-header">
              <div class="order-info">
                <h3 class="order-number">{{ order.orderNumber }}</h3>
                <p class="order-date">{{ formatDate(order.createdAt) }}</p>
              </div>

              <div class="order-status">
                <span
                  class="status-badge"
                  [style.background-color]="getStatusColor(order.status)">
                  {{ getOrderStatusIcon(order.status) }} {{ getStatusText(order.status) }}
                </span>
              </div>
            </div>

            <div class="order-details">
              <div class="order-summary">
                <div class="summary-item">
                  <span class="label">Articles:</span>
                  <span class="value">{{ order.itemCount }}</span>
                </div>
                <div class="summary-item">
                  <span class="label">Total:</span>
                  <span class="value total-amount">{{ order.total }} {{ order.currency }}</span>
                </div>
                @if (order.estimatedDelivery) {
                  <div class="summary-item">
                    <span class="label">Livraison prévue:</span>
                    <span class="value">{{ formatDate(order.estimatedDelivery) }}</span>
                  </div>
                }
              </div>

              <div class="order-actions">
                <button
                  routerLink="/orders/{{ order.id }}"
                  class="action-btn primary">
                  Voir détails
                </button>

                @if (canTrackOrder(order)) {
                  <button
                    (click)="onTrackOrder(order.id)"
                    class="action-btn secondary">
                    Suivre
                  </button>
                }

                @if (canCancelOrder(order)) {
                  <button
                    (click)="onCancelOrder(order.id)"
                    class="action-btn danger">
                    Annuler
                  </button>
                }

                <button
                  (click)="onReorderItems(order.id)"
                  class="action-btn outline">
                  Recommander
                </button>
              </div>
            </div>
          </div>
        }
      </div>
    } @else {
      <div class="empty-orders">
        <div class="empty-icon">📦</div>
        <h3>Aucune commande trouvée</h3>
        <p>
          @if (selectedStatus === 'all') {
            Vous n'avez pas encore passé de commande
          } @else {
            Aucune commande trouvée avec ce statut
          }
        </p>
        <a routerLink="/products" class="shop-btn">Découvrir nos produits</a>
      </div>
    }
  }
</div>

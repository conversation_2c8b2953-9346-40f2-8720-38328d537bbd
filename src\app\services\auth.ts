import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of, throwError } from 'rxjs';
import { delay, switchMap } from 'rxjs/operators';
import {
  User,
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  PasswordResetRequest,
  PasswordResetConfirm,
  UpdateProfileRequest,
  ChangePasswordRequest,
  ApiResponse
} from '../interfaces';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);

  // Mock users database
  private mockUsers: User[] = [
    {
      id: 1,
      email: '<EMAIL>',
      firstName: 'Amine',
      lastName: 'Hbili',
      phone: '+216 20 123 456',
      avatar: 'assets/images/avatar.jpg',
      addresses: [
        {
          id: 1,
          type: 'home',
          street: '123 Rue de la Liberté',
          city: 'Tunis',
          state: 'Tunis',
          postalCode: '1000',
          country: 'Tunisie',
          isDefault: true,
          firstName: 'Amine',
          lastName: 'Hbili',
          phone: '+216 20 123 456'
        }
      ],
      preferences: {
        language: 'fr',
        currency: 'TND',
        notifications: {
          email: true,
          sms: true,
          push: true,
          orderUpdates: true,
          promotions: false,
          newsletter: true
        },
        theme: 'light'
      },
      isActive: true,
      isVerified: true,
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date(),
      lastLoginAt: new Date()
    }
  ];

  constructor() {
    // Check if user is already logged in (from localStorage)
    this.loadUserFromStorage();
  }

  private loadUserFromStorage(): void {
    const storedUser = localStorage.getItem('currentUser');
    const storedToken = localStorage.getItem('authToken');

    if (storedUser && storedToken) {
      try {
        const user = JSON.parse(storedUser);
        this.currentUserSubject.next(user);
        this.isAuthenticatedSubject.next(true);
      } catch (error) {
        console.error('Error parsing stored user:', error);
        this.logout();
      }
    }
  }

  private saveUserToStorage(user: User, token: string): void {
    localStorage.setItem('currentUser', JSON.stringify(user));
    localStorage.setItem('authToken', token);
  }

  private clearUserFromStorage(): void {
    localStorage.removeItem('currentUser');
    localStorage.removeItem('authToken');
  }

  // Observables
  get currentUser$(): Observable<User | null> {
    return this.currentUserSubject.asObservable();
  }

  get isAuthenticated$(): Observable<boolean> {
    return this.isAuthenticatedSubject.asObservable();
  }

  get currentUser(): User | null {
    return this.currentUserSubject.value;
  }

  get isAuthenticated(): boolean {
    return this.isAuthenticatedSubject.value;
  }

  // Authentication methods
  login(request: LoginRequest): Observable<ApiResponse<AuthResponse>> {
    // Simulate API delay
    return of(null).pipe(
      delay(1000),
      switchMap(() => {
        // Find user by email
        const user = this.mockUsers.find(u => u.email === request.email);

        if (!user) {
          return of({
            success: false,
            message: 'Email non trouvé'
          });
        }

        // Mock password validation (in real app, this would be done server-side)
        if (request.password !== 'password123') {
          return of({
            success: false,
            message: 'Mot de passe incorrect'
          });
        }

        // Update last login
        user.lastLoginAt = new Date();

        // Generate mock token
        const token = 'mock-jwt-token-' + Date.now();
        const refreshToken = 'mock-refresh-token-' + Date.now();

        const authResponse: AuthResponse = {
          user,
          token,
          refreshToken,
          expiresIn: 3600 // 1 hour
        };

        // Save to storage if remember me
        if (request.rememberMe) {
          this.saveUserToStorage(user, token);
        }

        // Update subjects
        this.currentUserSubject.next(user);
        this.isAuthenticatedSubject.next(true);

        return of({
          success: true,
          data: authResponse,
          message: 'Connexion réussie'
        });
      })
    );
  }

  register(request: RegisterRequest): Observable<ApiResponse<AuthResponse>> {
    return of(null).pipe(
      delay(1500),
      switchMap(() => {
        // Check if email already exists
        const existingUser = this.mockUsers.find(u => u.email === request.email);

        if (existingUser) {
          return of({
            success: false,
            message: 'Cette adresse email est déjà utilisée'
          });
        }

        // Validate password confirmation
        if (request.password !== request.confirmPassword) {
          return of({
            success: false,
            message: 'Les mots de passe ne correspondent pas'
          });
        }

        // Create new user
        const newUser: User = {
          id: Date.now(),
          email: request.email,
          firstName: request.firstName,
          lastName: request.lastName,
          phone: request.phone,
          addresses: [],
          preferences: {
            language: 'fr',
            currency: 'TND',
            notifications: {
              email: true,
              sms: false,
              push: true,
              orderUpdates: true,
              promotions: true,
              newsletter: true
            },
            theme: 'light'
          },
          isActive: true,
          isVerified: false, // Would need email verification
          createdAt: new Date(),
          updatedAt: new Date()
        };

        // Add to mock database
        this.mockUsers.push(newUser);

        // Generate tokens
        const token = 'mock-jwt-token-' + Date.now();
        const refreshToken = 'mock-refresh-token-' + Date.now();

        const authResponse: AuthResponse = {
          user: newUser,
          token,
          refreshToken,
          expiresIn: 3600
        };

        // Save to storage
        this.saveUserToStorage(newUser, token);

        // Update subjects
        this.currentUserSubject.next(newUser);
        this.isAuthenticatedSubject.next(true);

        return of({
          success: true,
          data: authResponse,
          message: 'Inscription réussie'
        });
      })
    );
  }

  logout(): Observable<ApiResponse<boolean>> {
    // Clear storage
    this.clearUserFromStorage();

    // Update subjects
    this.currentUserSubject.next(null);
    this.isAuthenticatedSubject.next(false);

    return of({
      success: true,
      data: true,
      message: 'Déconnexion réussie'
    });
  }

  requestPasswordReset(request: PasswordResetRequest): Observable<ApiResponse<boolean>> {
    return of(null).pipe(
      delay(1000),
      switchMap(() => {
        const user = this.mockUsers.find(u => u.email === request.email);

        if (!user) {
          return of({
            success: false,
            message: 'Aucun compte trouvé avec cette adresse email'
          });
        }

        // In real app, would send email with reset link
        console.log('Password reset email sent to:', request.email);

        return of({
          success: true,
          data: true,
          message: 'Un email de réinitialisation a été envoyé'
        });
      })
    );
  }

  confirmPasswordReset(request: PasswordResetConfirm): Observable<ApiResponse<boolean>> {
    return of(null).pipe(
      delay(1000),
      switchMap(() => {
        // Validate token (mock validation)
        if (!request.token || request.token.length < 10) {
          return of({
            success: false,
            message: 'Token de réinitialisation invalide'
          });
        }

        // Validate password confirmation
        if (request.newPassword !== request.confirmPassword) {
          return of({
            success: false,
            message: 'Les mots de passe ne correspondent pas'
          });
        }

        // In real app, would update password in database
        console.log('Password reset confirmed');

        return of({
          success: true,
          data: true,
          message: 'Mot de passe réinitialisé avec succès'
        });
      })
    );
  }

  updateProfile(request: UpdateProfileRequest): Observable<ApiResponse<User>> {
    return of(null).pipe(
      delay(1000),
      switchMap(() => {
        const currentUser = this.currentUser;

        if (!currentUser) {
          return of({
            success: false,
            message: 'Utilisateur non connecté'
          });
        }

        // Update user data
        const updatedUser: User = {
          ...currentUser,
          ...request,
          updatedAt: new Date()
        };

        // Update in mock database
        const userIndex = this.mockUsers.findIndex(u => u.id === currentUser.id);
        if (userIndex >= 0) {
          this.mockUsers[userIndex] = updatedUser;
        }

        // Update storage
        const token = localStorage.getItem('authToken');
        if (token) {
          this.saveUserToStorage(updatedUser, token);
        }

        // Update subject
        this.currentUserSubject.next(updatedUser);

        return of({
          success: true,
          data: updatedUser,
          message: 'Profil mis à jour avec succès'
        });
      })
    );
  }

  changePassword(request: ChangePasswordRequest): Observable<ApiResponse<boolean>> {
    return of(null).pipe(
      delay(1000),
      switchMap(() => {
        // Validate current password (mock validation)
        if (request.currentPassword !== 'password123') {
          return of({
            success: false,
            message: 'Mot de passe actuel incorrect'
          });
        }

        // Validate password confirmation
        if (request.newPassword !== request.confirmPassword) {
          return of({
            success: false,
            message: 'Les nouveaux mots de passe ne correspondent pas'
          });
        }

        // In real app, would update password in database
        console.log('Password changed successfully');

        return of({
          success: true,
          data: true,
          message: 'Mot de passe modifié avec succès'
        });
      })
    );
  }

  // Helper methods
  hasPermission(permission: string): boolean {
    // Mock permission check
    return this.isAuthenticated;
  }

  refreshToken(): Observable<ApiResponse<string>> {
    // Mock token refresh
    const newToken = 'mock-jwt-token-refreshed-' + Date.now();

    const currentUser = this.currentUser;
    if (currentUser) {
      this.saveUserToStorage(currentUser, newToken);
    }

    return of({
      success: true,
      data: newToken,
      message: 'Token rafraîchi'
    });
  }
}

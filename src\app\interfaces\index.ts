// Central export file for all interfaces
// This makes importing interfaces easier throughout the application

// Product interfaces
export * from './product.interface';

// User interfaces  
export * from './user.interface';

// Cart interfaces
export * from './cart.interface';

// Order interfaces
export * from './order.interface';

// API interfaces
export * from './api.interface';

// Re-export commonly used types for convenience
export type { 
  Product, 
  ProductCategory, 
  ProductFilter 
} from './product.interface';

export type { 
  User, 
  Address, 
  LoginRequest, 
  RegisterRequest, 
  AuthResponse 
} from './user.interface';

export type { 
  Cart, 
  CartItem, 
  AddToCartRequest, 
  CartSummary 
} from './cart.interface';

export type { 
  Order, 
  OrderStatus, 
  OrderSummary, 
  CreateOrderRequest 
} from './order.interface';

export type { 
  ApiResponse, 
  LoadingState, 
  PaginationRequest 
} from './api.interface';

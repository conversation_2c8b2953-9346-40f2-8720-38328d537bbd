.orders-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
}

.header h2 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.subtitle {
  font-size: 1.1rem;
  color: #7f8c8d;
  margin: 0;
}

/* Filters Section */
.filters-section {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 2rem;
  margin-bottom: 2rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.filters-section h3 {
  font-size: 1.1rem;
  color: #2c3e50;
  margin-bottom: 1rem;
  font-weight: 600;
}

.filter-buttons,
.sort-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.filter-btn,
.sort-btn {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  color: #6c757d;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-btn:hover,
.sort-btn:hover {
  background: #e9ecef;
  border-color: #3498db;
  color: #3498db;
}

.filter-btn.active,
.sort-btn.active {
  background: #3498db;
  border-color: #3498db;
  color: white;
}

.sort-arrow {
  font-size: 0.8rem;
  font-weight: bold;
}

/* Loading & Error States */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container {
  display: flex;
  justify-content: center;
  padding: 4rem 2rem;
}

.error-message {
  text-align: center;
  padding: 2rem;
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 12px;
  max-width: 400px;
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.error-message p {
  color: #e53e3e;
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
}

.retry-btn {
  background: #e53e3e;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s ease;
}

.retry-btn:hover {
  background: #c53030;
}

/* Orders List */
.orders-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.order-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
}

.order-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.order-info h3 {
  font-size: 1.2rem;
  color: #2c3e50;
  margin-bottom: 0.25rem;
  font-weight: 700;
}

.order-date {
  color: #7f8c8d;
  font-size: 0.9rem;
  margin: 0;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  color: white;
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Order Details */
.order-details {
  padding: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  gap: 2rem;
}

.order-summary {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
}

.summary-item .label {
  color: #7f8c8d;
  font-size: 0.9rem;
  font-weight: 500;
}

.summary-item .value {
  color: #2c3e50;
  font-weight: 600;
}

.total-amount {
  color: #27ae60;
  font-size: 1.1rem;
  font-weight: 700;
}

/* Order Actions */
.order-actions {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.action-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 100px;
}

.action-btn.primary {
  background: #3498db;
  color: white;
  border: none;
}

.action-btn.primary:hover {
  background: #2980b9;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.action-btn.secondary {
  background: #95a5a6;
  color: white;
  border: none;
}

.action-btn.secondary:hover {
  background: #7f8c8d;
  transform: translateY(-2px);
}

.action-btn.danger {
  background: #e74c3c;
  color: white;
  border: none;
}

.action-btn.danger:hover {
  background: #c0392b;
  transform: translateY(-2px);
}

.action-btn.outline {
  background: transparent;
  color: #3498db;
  border: 2px solid #3498db;
}

.action-btn.outline:hover {
  background: #3498db;
  color: white;
}

/* Empty Orders */
.empty-orders {
  text-align: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  opacity: 0.5;
}

.empty-orders h3 {
  font-size: 1.8rem;
  color: #2c3e50;
  margin-bottom: 1rem;
}

.empty-orders p {
  color: #7f8c8d;
  font-size: 1.1rem;
  margin-bottom: 2rem;
}

.shop-btn {
  display: inline-block;
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  text-decoration: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.shop-btn:hover {
  background: linear-gradient(135deg, #2980b9, #1f5f8b);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .orders-container {
    padding: 1rem;
  }

  .header h2 {
    font-size: 2rem;
  }

  .filters-section {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 1.5rem;
  }

  .filter-buttons,
  .sort-buttons {
    gap: 0.5rem;
  }

  .filter-btn,
  .sort-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }

  .order-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .order-details {
    flex-direction: column;
    gap: 1.5rem;
    align-items: stretch;
  }

  .order-summary {
    grid-template-columns: 1fr;
  }

  .order-actions {
    justify-content: center;
  }

  .action-btn {
    flex: 1;
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .orders-container {
    padding: 0.5rem;
  }

  .header h2 {
    font-size: 1.8rem;
  }

  .filters-section {
    padding: 1rem;
  }

  .order-card {
    border-radius: 12px;
  }

  .order-header {
    padding: 1rem;
  }

  .order-details {
    padding: 1rem;
  }

  .action-btn {
    padding: 0.6rem 1rem;
    font-size: 0.85rem;
  }

  .order-actions {
    flex-direction: column;
  }
}
import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ProductDetailsComponent, Product } from './product-details';

describe('ProductDetailsComponent', () => {
  let component: ProductDetailsComponent;
  let fixture: ComponentFixture<ProductDetailsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ProductDetailsComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ProductDetailsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should display product details when product is provided', () => {
    const mockProduct: Product = {
      name: 'Test Product',
      price: 100,
      image: 'test.jpg',
      description: 'Test description'
    };

    component.product = mockProduct;
    fixture.detectChanges();

    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.querySelector('h2')?.textContent).toContain('Test Product');
    expect(compiled.querySelector('p')?.textContent).toContain('Test description');
  });

  it('should call onAddToCart when button is clicked', () => {
    const mockProduct: Product = {
      name: 'Test Product',
      price: 100,
      image: 'test.jpg'
    };

    component.product = mockProduct;
    spyOn(component, 'onAddToCart');
    fixture.detectChanges();

    const button = fixture.nativeElement.querySelector('button');
    button?.click();

    expect(component.onAddToCart).toHaveBeenCalled();
  });
});

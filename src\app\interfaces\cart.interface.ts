import { Product } from './product.interface';

// Cart interfaces for shopping cart management
export interface CartItem {
  id: number;
  product: Product;
  quantity: number;
  selectedOptions?: ProductOptions;
  addedAt: Date;
  unitPrice: number;
  totalPrice: number;
}

export interface ProductOptions {
  size?: string;
  color?: string;
  variant?: string;
  customization?: string;
}

export interface Cart {
  id: number;
  userId: number;
  items: CartItem[];
  subtotal: number;
  tax: number;
  shipping: number;
  discount: number;
  total: number;
  currency: string;
  createdAt: Date;
  updatedAt: Date;
  expiresAt?: Date;
}

export interface AddToCartRequest {
  productId: number;
  quantity: number;
  options?: ProductOptions;
}

export interface UpdateCartItemRequest {
  cartItemId: number;
  quantity: number;
  options?: ProductOptions;
}

export interface RemoveFromCartRequest {
  cartItemId: number;
}

export interface CartSummary {
  itemCount: number;
  subtotal: number;
  tax: number;
  shipping: number;
  discount: number;
  total: number;
  currency: string;
}

export interface ShippingOption {
  id: number;
  name: string;
  description: string;
  price: number;
  estimatedDays: number;
  isAvailable: boolean;
}

export interface DiscountCoupon {
  id: number;
  code: string;
  type: 'percentage' | 'fixed' | 'free_shipping';
  value: number;
  minOrderAmount?: number;
  maxDiscount?: number;
  isValid: boolean;
  expiresAt?: Date;
  description?: string;
}

export interface ApplyCouponRequest {
  couponCode: string;
}

export interface ApplyCouponResponse {
  success: boolean;
  coupon?: DiscountCoupon;
  discountAmount: number;
  message: string;
}

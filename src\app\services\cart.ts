import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import {
  Cart,
  CartItem,
  AddToCartRequest,
  UpdateCartItemRequest,
  RemoveFromCartRequest,
  CartSummary,
  ApiResponse,
  Product
} from '../interfaces';

@Injectable({
  providedIn: 'root'
})
export class CartService {
  private cartSubject = new BehaviorSubject<Cart | null>(null);
  private currentUserId = 1; // Mock user ID

  constructor() {
    this.initializeCart();
  }

  private initializeCart(): void {
    // Initialize with mock data
    const mockCart: Cart = {
      id: 1,
      userId: this.currentUserId,
      items: [
        {
          id: 1,
          product: {
            id: 1,
            name: 'Casque Shark Speed-R',
            price: 400,
            image: 'assets/images/casque.jpg',
            description: 'Casque intégral haute performance',
            category: { id: 1, name: 'Casques', slug: 'casques' },
            brand: 'Shark',
            inStock: true,
            quantity: 15,
            rating: 4.5
          },
          quantity: 1,
          addedAt: new Date(),
          unitPrice: 400,
          totalPrice: 400
        },
        {
          id: 2,
          product: {
            id: 2,
            name: 'Gants Alpinestars GP Pro',
            price: 120,
            image: 'assets/images/gants.jpg',
            description: 'Gants de moto en cuir',
            category: { id: 2, name: 'Gants', slug: 'gants' },
            brand: 'Alpinestars',
            inStock: true,
            quantity: 25,
            rating: 4.2
          },
          quantity: 2,
          addedAt: new Date(),
          unitPrice: 120,
          totalPrice: 240
        }
      ],
      subtotal: 640,
      tax: 64,
      shipping: 15,
      discount: 0,
      total: 719,
      currency: 'TND',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.cartSubject.next(mockCart);
  }

  getCart(): Observable<Cart | null> {
    return this.cartSubject.asObservable();
  }

  getCartSummary(): Observable<ApiResponse<CartSummary>> {
    const cart = this.cartSubject.value;
    if (!cart) {
      return of({
        success: false,
        message: 'Panier non trouvé'
      });
    }

    const summary: CartSummary = {
      itemCount: cart.items.reduce((sum, item) => sum + item.quantity, 0),
      subtotal: cart.subtotal,
      tax: cart.tax,
      shipping: cart.shipping,
      discount: cart.discount,
      total: cart.total,
      currency: cart.currency
    };

    return of({
      success: true,
      data: summary,
      message: 'Résumé du panier récupéré'
    });
  }

  addToCart(request: AddToCartRequest): Observable<ApiResponse<Cart>> {
    const currentCart = this.cartSubject.value;
    if (!currentCart) {
      return of({
        success: false,
        message: 'Erreur: Panier non initialisé'
      });
    }

    // Check if item already exists
    const existingItemIndex = currentCart.items.findIndex(
      item => item.product.id === request.productId
    );

    if (existingItemIndex >= 0) {
      // Update existing item
      currentCart.items[existingItemIndex].quantity += request.quantity;
      currentCart.items[existingItemIndex].totalPrice =
        currentCart.items[existingItemIndex].quantity *
        currentCart.items[existingItemIndex].unitPrice;
    } else {
      // Add new item (mock product data - in real app, fetch from product service)
      const mockProduct: Product = {
        id: request.productId,
        name: 'Nouveau Produit',
        price: 100,
        image: 'assets/images/default.jpg',
        description: 'Description du produit',
        category: { id: 1, name: 'Divers', slug: 'divers' },
        inStock: true
      };

      const newItem: CartItem = {
        id: Date.now(),
        product: mockProduct,
        quantity: request.quantity,
        selectedOptions: request.options,
        addedAt: new Date(),
        unitPrice: mockProduct.price,
        totalPrice: mockProduct.price * request.quantity
      };

      currentCart.items.push(newItem);
    }

    this.recalculateCart(currentCart);
    this.cartSubject.next(currentCart);

    return of({
      success: true,
      data: currentCart,
      message: 'Produit ajouté au panier'
    });
  }

  updateCartItem(request: UpdateCartItemRequest): Observable<ApiResponse<Cart>> {
    const currentCart = this.cartSubject.value;
    if (!currentCart) {
      return of({
        success: false,
        message: 'Erreur: Panier non trouvé'
      });
    }

    const itemIndex = currentCart.items.findIndex(item => item.id === request.cartItemId);
    if (itemIndex === -1) {
      return of({
        success: false,
        message: 'Article non trouvé dans le panier'
      });
    }

    if (request.quantity <= 0) {
      currentCart.items.splice(itemIndex, 1);
    } else {
      currentCart.items[itemIndex].quantity = request.quantity;
      currentCart.items[itemIndex].totalPrice =
        request.quantity * currentCart.items[itemIndex].unitPrice;

      if (request.options) {
        currentCart.items[itemIndex].selectedOptions = request.options;
      }
    }

    this.recalculateCart(currentCart);
    this.cartSubject.next(currentCart);

    return of({
      success: true,
      data: currentCart,
      message: 'Panier mis à jour'
    });
  }

  removeFromCart(request: RemoveFromCartRequest): Observable<ApiResponse<Cart>> {
    const currentCart = this.cartSubject.value;
    if (!currentCart) {
      return of({
        success: false,
        message: 'Erreur: Panier non trouvé'
      });
    }

    const itemIndex = currentCart.items.findIndex(item => item.id === request.cartItemId);
    if (itemIndex === -1) {
      return of({
        success: false,
        message: 'Article non trouvé dans le panier'
      });
    }

    currentCart.items.splice(itemIndex, 1);
    this.recalculateCart(currentCart);
    this.cartSubject.next(currentCart);

    return of({
      success: true,
      data: currentCart,
      message: 'Article supprimé du panier'
    });
  }

  clearCart(): Observable<ApiResponse<boolean>> {
    const currentCart = this.cartSubject.value;
    if (currentCart) {
      currentCart.items = [];
      this.recalculateCart(currentCart);
      this.cartSubject.next(currentCart);
    }

    return of({
      success: true,
      data: true,
      message: 'Panier vidé'
    });
  }

  private recalculateCart(cart: Cart): void {
    cart.subtotal = cart.items.reduce((sum, item) => sum + item.totalPrice, 0);
    cart.tax = Math.round(cart.subtotal * 0.1); // 10% tax
    cart.shipping = cart.subtotal > 500 ? 0 : 15; // Free shipping over 500 TND
    cart.total = cart.subtotal + cart.tax + cart.shipping - cart.discount;
    cart.updatedAt = new Date();
  }
}

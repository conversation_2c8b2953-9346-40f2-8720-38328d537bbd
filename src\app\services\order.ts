import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import {
  Order,
  OrderSummary,
  OrderStatus,
  CreateOrderRequest,
  OrderFilter,
  OrderSearchResult,
  ApiResponse
} from '../interfaces';

@Injectable({
  providedIn: 'root'
})
export class OrderService {
  private mockOrders: Order[] = [
    {
      id: 1,
      orderNumber: 'ORD-2025-001',
      userId: 1,
      status: 'delivered',
      items: [
        {
          id: 1,
          product: {
            id: 1,
            name: 'Casque Shark Speed-R',
            price: 400,
            image: 'assets/images/casque.jpg',
            description: 'Casque intégral haute performance',
            category: { id: 1, name: 'Casques', slug: 'casques' },
            brand: 'Shark',
            inStock: true
          },
          quantity: 1,
          unitPrice: 400,
          totalPrice: 400
        }
      ],
      shippingAddress: {
        id: 1,
        type: 'home',
        street: '123 Rue de la Liberté',
        city: 'Tunis',
        state: 'Tunis',
        postalCode: '1000',
        country: 'Tunisie',
        isDefault: true,
        firstName: 'Amine',
        lastName: 'Hbili'
      },
      billingAddress: {
        id: 1,
        type: 'home',
        street: '123 Rue de la Liberté',
        city: 'Tunis',
        state: 'Tunis',
        postalCode: '1000',
        country: 'Tunisie',
        isDefault: true,
        firstName: 'Amine',
        lastName: 'Hbili'
      },
      payment: {
        method: 'credit_card',
        status: 'completed',
        transactionId: 'TXN-123456',
        amount: 459,
        currency: 'TND',
        paidAt: new Date('2025-10-01T10:30:00')
      },
      shipping: {
        option: {
          id: 1,
          name: 'Livraison Standard',
          description: 'Livraison en 3-5 jours ouvrables',
          price: 15,
          estimatedDays: 4,
          isAvailable: true
        },
        trackingNumber: 'TRK-789012',
        carrier: 'Aramex',
        estimatedDelivery: new Date('2025-10-05'),
        actualDelivery: new Date('2025-10-04'),
        shippedAt: new Date('2025-10-02')
      },
      pricing: {
        subtotal: 400,
        tax: 40,
        shipping: 15,
        discount: 0,
        total: 455,
        currency: 'TND'
      },
      timeline: [
        {
          id: 1,
          status: 'pending',
          message: 'Commande reçue',
          createdAt: new Date('2025-10-01T10:00:00')
        },
        {
          id: 2,
          status: 'confirmed',
          message: 'Commande confirmée',
          createdAt: new Date('2025-10-01T11:00:00')
        },
        {
          id: 3,
          status: 'shipped',
          message: 'Commande expédiée',
          createdAt: new Date('2025-10-02T09:00:00')
        },
        {
          id: 4,
          status: 'delivered',
          message: 'Commande livrée',
          createdAt: new Date('2025-10-04T14:30:00')
        }
      ],
      createdAt: new Date('2025-10-01T10:00:00'),
      updatedAt: new Date('2025-10-04T14:30:00')
    },
    {
      id: 2,
      orderNumber: 'ORD-2025-002',
      userId: 1,
      status: 'shipped',
      items: [
        {
          id: 2,
          product: {
            id: 2,
            name: 'Gants Alpinestars GP Pro',
            price: 120,
            image: 'assets/images/gants.jpg',
            description: 'Gants de moto en cuir',
            category: { id: 2, name: 'Gants', slug: 'gants' },
            brand: 'Alpinestars',
            inStock: true
          },
          quantity: 2,
          unitPrice: 120,
          totalPrice: 240
        },
        {
          id: 3,
          product: {
            id: 3,
            name: 'Blouson Dainese Racing',
            price: 350,
            image: 'assets/images/blouson.jpg',
            description: 'Blouson en cuir pour moto',
            category: { id: 3, name: 'Blousons', slug: 'blousons' },
            brand: 'Dainese',
            inStock: true
          },
          quantity: 1,
          unitPrice: 350,
          totalPrice: 350
        }
      ],
      shippingAddress: {
        id: 1,
        type: 'home',
        street: '123 Rue de la Liberté',
        city: 'Tunis',
        state: 'Tunis',
        postalCode: '1000',
        country: 'Tunisie',
        isDefault: true,
        firstName: 'Amine',
        lastName: 'Hbili'
      },
      billingAddress: {
        id: 1,
        type: 'home',
        street: '123 Rue de la Liberté',
        city: 'Tunis',
        state: 'Tunis',
        postalCode: '1000',
        country: 'Tunisie',
        isDefault: true,
        firstName: 'Amine',
        lastName: 'Hbili'
      },
      payment: {
        method: 'credit_card',
        status: 'completed',
        transactionId: 'TXN-789012',
        amount: 649,
        currency: 'TND',
        paidAt: new Date('2025-10-02T15:20:00')
      },
      shipping: {
        option: {
          id: 1,
          name: 'Livraison Standard',
          description: 'Livraison en 3-5 jours ouvrables',
          price: 15,
          estimatedDays: 4,
          isAvailable: true
        },
        trackingNumber: 'TRK-345678',
        carrier: 'Aramex',
        estimatedDelivery: new Date('2025-10-07'),
        shippedAt: new Date('2025-10-03')
      },
      pricing: {
        subtotal: 590,
        tax: 59,
        shipping: 0, // Free shipping over 500 TND
        discount: 0,
        total: 649,
        currency: 'TND'
      },
      timeline: [
        {
          id: 1,
          status: 'pending',
          message: 'Commande reçue',
          createdAt: new Date('2025-10-02T15:00:00')
        },
        {
          id: 2,
          status: 'confirmed',
          message: 'Commande confirmée',
          createdAt: new Date('2025-10-02T16:00:00')
        },
        {
          id: 3,
          status: 'shipped',
          message: 'Commande expédiée',
          createdAt: new Date('2025-10-03T10:00:00')
        }
      ],
      createdAt: new Date('2025-10-02T15:00:00'),
      updatedAt: new Date('2025-10-03T10:00:00')
    }
  ];

  getUserOrders(userId: number, filter?: OrderFilter): Observable<ApiResponse<OrderSearchResult>> {
    let filteredOrders = this.mockOrders.filter(order => order.userId === userId);

    // Apply filters
    if (filter?.status && filter.status.length > 0) {
      filteredOrders = filteredOrders.filter(order =>
        filter.status!.includes(order.status)
      );
    }

    if (filter?.dateFrom) {
      filteredOrders = filteredOrders.filter(order =>
        order.createdAt >= filter.dateFrom!
      );
    }

    if (filter?.dateTo) {
      filteredOrders = filteredOrders.filter(order =>
        order.createdAt <= filter.dateTo!
      );
    }

    if (filter?.minAmount !== undefined) {
      filteredOrders = filteredOrders.filter(order =>
        order.pricing.total >= filter.minAmount!
      );
    }

    if (filter?.maxAmount !== undefined) {
      filteredOrders = filteredOrders.filter(order =>
        order.pricing.total <= filter.maxAmount!
      );
    }

    // Apply sorting
    if (filter?.sortBy) {
      filteredOrders.sort((a, b) => {
        let aValue: any, bValue: any;

        switch (filter.sortBy) {
          case 'date':
            aValue = a.createdAt;
            bValue = b.createdAt;
            break;
          case 'amount':
            aValue = a.pricing.total;
            bValue = b.pricing.total;
            break;
          case 'status':
            aValue = a.status;
            bValue = b.status;
            break;
          default:
            return 0;
        }

        if (filter.sortOrder === 'desc') {
          return bValue > aValue ? 1 : -1;
        }
        return aValue > bValue ? 1 : -1;
      });
    }

    // Convert to summaries
    const orderSummaries: OrderSummary[] = filteredOrders.map(order => ({
      id: order.id,
      orderNumber: order.orderNumber,
      status: order.status,
      total: order.pricing.total,
      currency: order.pricing.currency,
      itemCount: order.items.reduce((sum, item) => sum + item.quantity, 0),
      createdAt: order.createdAt,
      estimatedDelivery: order.shipping.estimatedDelivery
    }));

    const result: OrderSearchResult = {
      orders: orderSummaries,
      totalCount: orderSummaries.length,
      currentPage: 1,
      totalPages: 1
    };

    return of({
      success: true,
      data: result,
      message: 'Commandes récupérées avec succès'
    });
  }

  getOrderById(orderId: number): Observable<ApiResponse<Order>> {
    const order = this.mockOrders.find(o => o.id === orderId);

    return of({
      success: !!order,
      data: order,
      message: order ? 'Commande trouvée' : 'Commande non trouvée'
    });
  }

  createOrder(request: CreateOrderRequest): Observable<ApiResponse<Order>> {
    // Mock order creation
    const newOrder: Order = {
      id: Date.now(),
      orderNumber: `ORD-2025-${String(Date.now()).slice(-3)}`,
      userId: 1, // Mock user ID
      status: 'pending',
      items: request.items.map(item => ({
        id: Date.now() + Math.random(),
        product: item.product,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        totalPrice: item.totalPrice
      })),
      shippingAddress: {
        id: request.shippingAddressId,
        type: 'home',
        street: '123 Rue Mock',
        city: 'Tunis',
        state: 'Tunis',
        postalCode: '1000',
        country: 'Tunisie',
        isDefault: true
      },
      billingAddress: {
        id: request.billingAddressId,
        type: 'home',
        street: '123 Rue Mock',
        city: 'Tunis',
        state: 'Tunis',
        postalCode: '1000',
        country: 'Tunisie',
        isDefault: true
      },
      payment: {
        method: request.paymentMethod,
        status: 'pending',
        amount: request.items.reduce((sum, item) => sum + item.totalPrice, 0),
        currency: 'TND'
      },
      shipping: {
        option: {
          id: request.shippingOptionId,
          name: 'Livraison Standard',
          description: 'Livraison en 3-5 jours',
          price: 15,
          estimatedDays: 4,
          isAvailable: true
        }
      },
      pricing: {
        subtotal: request.items.reduce((sum, item) => sum + item.totalPrice, 0),
        tax: 0,
        shipping: 15,
        discount: 0,
        total: request.items.reduce((sum, item) => sum + item.totalPrice, 0) + 15,
        currency: 'TND'
      },
      timeline: [
        {
          id: 1,
          status: 'pending',
          message: 'Commande reçue',
          createdAt: new Date()
        }
      ],
      notes: request.notes,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.mockOrders.push(newOrder);

    return of({
      success: true,
      data: newOrder,
      message: 'Commande créée avec succès'
    });
  }

  getOrderStatusText(status: OrderStatus): string {
    const statusMap: Record<OrderStatus, string> = {
      'pending': 'En attente',
      'confirmed': 'Confirmée',
      'processing': 'En préparation',
      'shipped': 'Expédiée',
      'delivered': 'Livrée',
      'cancelled': 'Annulée',
      'refunded': 'Remboursée',
      'returned': 'Retournée'
    };

    return statusMap[status] || status;
  }

  getOrderStatusColor(status: OrderStatus): string {
    const colorMap: Record<OrderStatus, string> = {
      'pending': '#f39c12',
      'confirmed': '#3498db',
      'processing': '#9b59b6',
      'shipped': '#2980b9',
      'delivered': '#27ae60',
      'cancelled': '#e74c3c',
      'refunded': '#e67e22',
      'returned': '#95a5a6'
    };

    return colorMap[status] || '#7f8c8d';
  }
}

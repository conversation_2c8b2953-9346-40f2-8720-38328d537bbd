import { Product } from './product.interface';
import { Address } from './user.interface';
import { CartItem, ShippingOption, DiscountCoupon } from './cart.interface';

// Order interfaces for order management
export interface Order {
  id: number;
  orderNumber: string;
  userId: number;
  status: OrderStatus;
  items: OrderItem[];
  shippingAddress: Address;
  billingAddress: Address;
  payment: PaymentInfo;
  shipping: ShippingInfo;
  pricing: OrderPricing;
  timeline: OrderTimeline[];
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface OrderItem {
  id: number;
  product: Product;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  selectedOptions?: {
    size?: string;
    color?: string;
    variant?: string;
  };
}

export type OrderStatus = 
  | 'pending'
  | 'confirmed'
  | 'processing'
  | 'shipped'
  | 'delivered'
  | 'cancelled'
  | 'refunded'
  | 'returned';

export interface PaymentInfo {
  method: PaymentMethod;
  status: PaymentStatus;
  transactionId?: string;
  amount: number;
  currency: string;
  paidAt?: Date;
  refundedAt?: Date;
  refundAmount?: number;
}

export type PaymentMethod = 
  | 'credit_card'
  | 'debit_card'
  | 'paypal'
  | 'bank_transfer'
  | 'cash_on_delivery'
  | 'mobile_payment';

export type PaymentStatus = 
  | 'pending'
  | 'processing'
  | 'completed'
  | 'failed'
  | 'cancelled'
  | 'refunded'
  | 'partially_refunded';

export interface ShippingInfo {
  option: ShippingOption;
  trackingNumber?: string;
  carrier?: string;
  estimatedDelivery?: Date;
  actualDelivery?: Date;
  shippedAt?: Date;
}

export interface OrderPricing {
  subtotal: number;
  tax: number;
  shipping: number;
  discount: number;
  total: number;
  currency: string;
  appliedCoupons?: DiscountCoupon[];
}

export interface OrderTimeline {
  id: number;
  status: OrderStatus;
  message: string;
  createdAt: Date;
  createdBy?: string;
}

// Request/Response interfaces
export interface CreateOrderRequest {
  items: CartItem[];
  shippingAddressId: number;
  billingAddressId: number;
  shippingOptionId: number;
  paymentMethod: PaymentMethod;
  couponCode?: string;
  notes?: string;
}

export interface OrderSummary {
  id: number;
  orderNumber: string;
  status: OrderStatus;
  total: number;
  currency: string;
  itemCount: number;
  createdAt: Date;
  estimatedDelivery?: Date;
}

export interface OrderFilter {
  status?: OrderStatus[];
  dateFrom?: Date;
  dateTo?: Date;
  minAmount?: number;
  maxAmount?: number;
  sortBy?: 'date' | 'amount' | 'status';
  sortOrder?: 'asc' | 'desc';
}

export interface OrderSearchResult {
  orders: OrderSummary[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
}

import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { Subscription } from 'rxjs';
import { CartService } from '../../../services/cart';
import {
  Cart,
  CartItem,
  CartSummary,
  LoadingState,
  UpdateCartItemRequest,
  RemoveFromCartRequest
} from '../../../interfaces';

@Component({
  selector: 'app-cart',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './cart.html',
  styleUrls: ['./cart.css']
})
export class CartComponent implements OnInit, OnDestroy {
  cart: Cart | null = null;
  cartSummary: CartSummary | null = null;
  loadingState: LoadingState = {
    isLoading: false,
    error: undefined
  };

  private subscriptions: Subscription[] = [];

  constructor(private cartService: CartService) {}

  ngOnInit(): void {
    this.loadCart();
    this.loadCartSummary();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  private loadCart(): void {
    const cartSub = this.cartService.getCart().subscribe({
      next: (cart) => {
        this.cart = cart;
      },
      error: (error) => {
        console.error('Error loading cart:', error);
        this.loadingState.error = 'Erreur lors du chargement du panier';
      }
    });

    this.subscriptions.push(cartSub);
  }

  loadCartSummary(): void {
    this.loadingState.isLoading = true;

    const summarySub = this.cartService.getCartSummary().subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.cartSummary = response.data;
        } else {
          this.loadingState.error = response.message || 'Erreur lors du chargement du résumé';
        }
        this.loadingState.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading cart summary:', error);
        this.loadingState.error = 'Erreur de connexion';
        this.loadingState.isLoading = false;
      }
    });

    this.subscriptions.push(summarySub);
  }

  updateQuantity(cartItemId: number, newQuantity: number): void {
    if (newQuantity < 0) return;

    const request: UpdateCartItemRequest = {
      cartItemId,
      quantity: newQuantity
    };

    this.cartService.updateCartItem(request).subscribe({
      next: (response) => {
        if (response.success) {
          this.loadCartSummary(); // Refresh summary
        } else {
          console.error('Error updating cart:', response.message);
        }
      },
      error: (error) => {
        console.error('Error updating cart item:', error);
      }
    });
  }

  removeItem(cartItemId: number): void {
    const request: RemoveFromCartRequest = { cartItemId };

    this.cartService.removeFromCart(request).subscribe({
      next: (response) => {
        if (response.success) {
          this.loadCartSummary(); // Refresh summary
        } else {
          console.error('Error removing item:', response.message);
        }
      },
      error: (error) => {
        console.error('Error removing cart item:', error);
      }
    });
  }

  clearCart(): void {
    if (confirm('Êtes-vous sûr de vouloir vider votre panier ?')) {
      this.cartService.clearCart().subscribe({
        next: (response) => {
          if (response.success) {
            this.loadCartSummary(); // Refresh summary
          } else {
            console.error('Error clearing cart:', response.message);
          }
        },
        error: (error) => {
          console.error('Error clearing cart:', error);
        }
      });
    }
  }

  trackByItemId(index: number, item: CartItem): number {
    return item.id;
  }

  get isEmpty(): boolean {
    return !this.cart || this.cart.items.length === 0;
  }

  get itemCount(): number {
    return this.cartSummary?.itemCount || 0;
  }

  get total(): number {
    return this.cartSummary?.total || 0;
  }
}

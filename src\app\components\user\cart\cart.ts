import { Component } from '@angular/core';

@Component({
  selector: 'app-cart',
  standalone: true,
  templateUrl: './cart.html',
  styleUrls: ['./cart.css']
})
export class CartComponent {
  cartItems = [
    { name: 'Casque Shark', price: 400, qty: 1 },
    { name: 'Gants Moto', price: 120, qty: 2 }
  ];

  get total() {
    return this.cartItems.reduce((sum, i) => sum + i.price * i.qty, 0);
  }
}
